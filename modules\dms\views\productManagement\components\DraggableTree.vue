<template>
    <div class="draggable-tree">
        <el-tree
            ref="tree"
            :data="treeData"
            :props="props"
            :default-expand-all="expandAll"
            :expand-on-click-node="false"
            node-key="id"
            draggable
            :allow-drop="allowDrop"
            @node-drop="handleNodeDrop"
        >
            <template #default="{ node, data }">
                <div class="tree-node">
                    <div v-if="node.level === 1">
                        <span class="node-sort">P</span>
                        {{ productName }}
                    </div>
                    <div
                        class="node-label"
                        v-else-if="!data.editing"
                        @click="canEditOrDelete(data) ? startEdit(data) : null"
                        :class="{ disabled: !canEditOrDelete(data) }"
                    >
                        <span class="node-sort">{{ `${data.moduleType || ''}${data.sort || ''}` }}</span>
                        {{ node.label }}
                    </div>
                    <el-input
                        v-else
                        v-model="data.editingLabel"
                        size="mini"
                        class="node-input"
                        @blur="finishEdit(data)"
                        @keyup.enter="finishEdit(data)"
                        @keyup.esc="cancelEdit(data)"
                        ref="editInput"
                    />
                    <div class="node-actions">
                        <!-- 第一级显示选择弹窗（团队leader只能添加模块） -->
                        <el-popover
                            v-if="canAddChild(data) && node.level === 1 && !isTeamLeader"
                            placement="bottom"
                            width="160"
                            trigger="click"
                            class="add-node-popover"
                            :ref="`popover-${data.id || node.id}`"
                        >
                            <div class="node-type-selector">
                                <div class="selector-title">选择节点类型</div>
                                <div class="selector-options">
                                    <el-button
                                        size="small"
                                        type="primary"
                                        @click="confirmAddNode(node, data, 'product')"
                                        class="type-button"
                                    >
                                        产品
                                    </el-button>
                                    <el-button
                                        size="small"
                                        type="success"
                                        @click="confirmAddNode(node, data, 'module')"
                                        class="type-button"
                                    >
                                        模块
                                    </el-button>
                                </div>
                            </div>
                            <el-button slot="reference" type="text" size="mini" icon="el-icon-plus" @click.stop />
                        </el-popover>
                        <!-- 团队leader在第一级或其他级别都直接添加模块 -->
                        <el-button
                            v-else-if="canAddChild(data) && node.level !== 1"
                            type="text"
                            size="mini"
                            icon="el-icon-plus"
                            @click.stop="addModuleDirectly(node, data)"
                        />
                        <el-button
                            v-if="canEditOrDelete(data)"
                            type="text"
                            size="mini"
                            icon="el-icon-delete"
                            @click.stop="removeNode(node, data)"
                        />
                    </div>
                </div>
            </template>
        </el-tree>
    </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid';

export default {
    name: 'DraggableTree',
    props: {
        data: {
            type: Array,
            default: () => []
        },
        props: {
            type: Object,
            default: () => ({
                children: 'children',
                label: 'label'
            })
        },
        expandAll: {
            type: Boolean,
            default: true
        },
        maxLevel: {
            type: Number,
            default: 4
        },
        // 是否为团队leader，团队leader只能添加、编辑、删除模块
        isTeamLeader: {
            type: Boolean,
            default: false
        },
        // 根节点产品名称
        productName: {
            type: String,
            default: '',
            required: true
        },
        // 是否为新增模式
        // 两种模式：新增和变更，新增模式下，所有节点都是空状态
        isAdd: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            internalTreeData: null,
            deletedNodeIdList: []
        };
    },
    computed: {
        treeData: {
            get() {
                // 如果有内部数据，使用内部数据；否则使用 props 传入的数据
                return this.internalTreeData || this.data;
            },
            set(value) {
                this.internalTreeData = value;
            }
        }
    },
    methods: {
        /**
         * 判断节点能否被拖拽
         * 规则：1.子产品只能在产品内部拖动
         * 2.模块只能拖动到产品、子产品里面
         * 3.子模块只能拖动到模块里面
         * @param {Object} draggingNode 拖拽中的节点
         * @param {Object} dropNode 拖拽到的节点
         * @param {String} dropType 拖拽类型
         * @returns {boolean} 是否允许
         */
        allowDrop(draggingNode, dropNode, dropType) {
            // 获取节点的业务类型
            const getNodeType = (data, level) => {
                // 根节点一定是产品
                if (level === 1) return 'product';
                return data.moduleType;
            };

            // 判断容器类型是否为产品类型
            const isProductType = (type) => type === 'product' || type === 'P';

            // 判断容器类型是否为产品或子产品类型
            const isProductOrSubProduct = (type) => isProductType(type) || type === 'SP';

            // 根据拖拽节点类型判断是否允许放入指定容器
            const canDropInContainer = (draggingType, containerType) => {
                switch (draggingType) {
                    case 'SP':
                        // 子产品只能拖动到产品内部
                        return isProductType(containerType);
                    case 'M':
                        // 模块只能拖动到产品、子产品内部
                        return isProductOrSubProduct(containerType);
                    case 'SM':
                        // 子模块只能拖动到模块内部
                        return containerType === 'M';
                    default:
                        return false;
                }
            };

            const draggingType = getNodeType(draggingNode.data, draggingNode.level);

            if (dropType === 'inner') {
                // inner 类型：拖拽到节点内部
                const dropContainerType = getNodeType(dropNode.data, dropNode.level);
                return canDropInContainer(draggingType, dropContainerType);
            } else if (dropType === 'prev' || dropType === 'next') {
                // prev/next 类型：拖拽到节点前后（同级）
                // 必须是同级节点
                if (draggingNode.parent !== dropNode.parent) {
                    return false;
                }

                // 获取父节点类型作为容器类型
                const parentType = draggingNode.parent
                    ? getNodeType(draggingNode.parent.data, draggingNode.parent.level)
                    : 'root';

                return canDropInContainer(draggingType, parentType);
            }

            return false;
        },

        /**
         * 拖拽成功完成时触发的事件
         * @param {Object} draggingNode 拖拽中的节点
         * @param {Object} dropNode 拖拽到的节点
         * @param {String} dropType 拖拽类型
         */
        handleNodeDrop(draggingNode, dropNode, dropType) {
            this.initInternalData();

            // 判断是否为同级拖拽
            const isSameLevelDrag = this.isSameLevelDrop(draggingNode, dropNode, dropType);

            // 在新增模式下，所有拖拽都设置为新增状态
            if (this.isAdd) {
                const draggingData = draggingNode.data;
                this.setNodeEditType(draggingData, 'add');
            }
                // 只有非同级拖拽才算删除和新增
                if (!isSameLevelDrag) {
                    const draggingData = draggingNode.data;

                    // 1. 收集拖拽节点及其所有子节点的ID到删除列表
                    this.collectDeletedNodeIds(draggingData);

                    // 2. 设置拖拽节点及其所有子节点为新增状态
                    this.setNodeEditType(draggingData, 'add');
                }
            

            // 拖拽完成后更新索引
            this.$nextTick(() => {
                this.updateIndexes();
                this.$emit('node-drop', {
                    draggingNode,
                    dropNode,
                    dropType,
                    isSameLevelDrag,
                    treeData: this.treeData
                });
            });
        },

        /**
         * 判断是否为同级拖拽
         * @param {Object} draggingNode - 拖拽节点
         * @param {Object} dropNode - 目标节点
         * @param {String} dropType - 拖拽类型
         * @returns {boolean} 是否为同级拖拽
         */
        isSameLevelDrop(draggingNode, dropNode, dropType) {
            // 注意这里参数和拖拽中的不一样
            // 只有 before 和 after 类型才可能是同级拖拽
            if (dropType !== 'before' && dropType !== 'after') {
                return false;
            }

            // 如果层级不同，肯定不是同级
            if (draggingNode.level !== dropNode.level) {
                return false;
            }

            // 通过在树数据中查找实际的父节点来判断
            const draggingParent = this.findNodeParent(draggingNode.data.id);

            const dropParent = this.findNodeParent(dropNode.data.id);

            // 比较父节点ID来判断是否同级
            return draggingParent?.id === dropParent?.id;
        },

        /**
         * 在树数据中查找指定节点的父节点
         * @param {string|number} nodeId - 节点ID
         * @returns {Object|null} 父节点数据，如果是根节点则返回null
         */
        findNodeParent(nodeId) {
            const findParent = (nodes, targetId, parent = null) => {
                for (const node of nodes) {
                    if (node.id === targetId) {
                        return parent;
                    }
                    if (node.children && node.children.length > 0) {
                        const result = findParent(node.children, targetId, node);
                        if (result !== undefined) {
                            return result;
                        }
                    }
                }
                return undefined;
            };

            return findParent(this.treeData, nodeId);
        },

        /**
         * 开始编辑节点
         * @param {Object} data - 节点数据
         */
        startEdit(data) {
            // 检查权限
            if (!this.canEditOrDelete(data)) {
                this.$message.warning('您没有权限编辑此节点');
                return;
            }

            // 先取消其他节点的编辑状态
            this.cancelAllEditing();

            this.$set(data, 'editing', true);
            this.$set(data, 'editingLabel', data.moduleName);

            // 下一帧聚焦输入框
            this.$nextTick(() => {
                const inputs = this.$refs.editInput;
                if (inputs) {
                    const input = Array.isArray(inputs) ? inputs[inputs.length - 1] : inputs;
                    input.focus();
                    input.select();
                }
            });
        },

        /**
         * 完成编辑
         * @param {Object} data - 节点数据
         */
        finishEdit(data) {
            if (!data.editing) return;

            const newLabel = data.editingLabel?.trim();
            if (newLabel && newLabel !== data.moduleName) {
                const oldLabel = data.moduleName;

                // 如果还没有 originalLabel，保存原始名称
                if (!data.originalLabel) {
                    this.$set(data, 'originalLabel', oldLabel);
                }

                // 设置编辑类型：如果是新增模式，所有编辑都是新增状态
                const editType = this.isAdd ? '' : 'edit';
                this.$set(data, 'editType', editType);
                data.moduleName = newLabel;

                this.$emit('label-change', {
                    data,
                    oldLabel,
                    newLabel,
                    treeData: this.treeData
                });
            }

            this.$set(data, 'editing', false);
            this.$delete(data, 'editingLabel');
        },

        /**
         * 取消编辑
         * @param {Object} data - 节点
         */
        cancelEdit(data) {
            this.$set(data, 'editing', false);
            this.$delete(data, 'editingLabel');
        },

        /**
         * 取消所有节点的编辑状态
         */
        cancelAllEditing() {
            const cancelEditingRecursive = (nodes) => {
                nodes.forEach((node) => {
                    if (node.editing) {
                        this.$set(node, 'editing', false);
                        this.$delete(node, 'editingLabel');
                    }
                    if (node.children && node.children.length > 0) {
                        cancelEditingRecursive(node.children);
                    }
                });
            };

            cancelEditingRecursive(this.treeData);
        },

        /**
         * 检查是否可以添加子节点
         * @param {Object} data - 节点数据
         * @returns {boolean} 是否可以添加
         */
        canAddChild(data) {
            // 只要不是子模块节点就可以
            return data.moduleType !== 'SM';
        },

        /**
         * 检查是否可以编辑或删除节点
         * @param {Object} data  - 节点数据
         * @returns {boolean} 是否可以编辑或删除
         */
        canEditOrDelete(data) {
            // 如果是团队leader，只能操作模块类型的节点
            if (this.isTeamLeader) {
                return data.type === 'module';
            }
            // 非团队leader可以操作所有节点
            return true;
        },
        /**
         * 确认添加节点（选择类型后）
         * @param {Object} node 父节点node
         * @param {Object} data 父节点数据
         * @param {String} nodeType 节点类型
         */
        confirmAddNode(node, data, nodeType) {
            this.initInternalData();
            const newNodeId = this.generateId();
            const typeLabel = nodeType === 'product' ? '产品' : '模块';
            let moduleType = 'SP';
            if (typeLabel === '模块') {
                // 如果父节点的 moduleType 已经是 'M'，则设置为 'SM'，否则设置为 'M'
                moduleType = data.moduleType === 'M' ? 'SM' : 'M';
            }
            const newNode = {
                id: newNodeId,
                productId: newNodeId,
                moduleName: `新${typeLabel}`,
                type: nodeType,
                moduleType,
                editType: 'add',
                children: []
            };

            if (!data.children) {
                this.$set(data, 'children', []);
            }

            data.children.push(newNode);

            // 更新索引
            this.updateIndexes();

            // 关闭 popover
            const popoverRef = this.$refs[`popover-${data.id || node.id}`];
            if (popoverRef) {
                popoverRef.doClose();
            }

            this.$emit('add-node', {
                parentNode: node,
                parentData: data,
                newNode,
                nodeType,
                level: node.level,
                treeData: this.treeData
            });
        },

        /**
         * 直接添加模块
         * @param {Object} node 父节点node
         * @param {Object} data 父节点数据
         */
        addModuleDirectly(node, data) {
            this.initInternalData();
            const newNodeId = this.generateId();
            // 如果父节点的 moduleType 已经是 'M'，则设置为 'SM'，否则设置为 'M'
            const moduleType = data.moduleType === 'M' ? 'SM' : 'M';
            const newNode = {
                id: newNodeId,
                productId: newNodeId,
                moduleName: '新模块',
                type: 'module',
                sort: '',
                moduleType,
                editType: 'add',
                children: []
            };

            if (!data.children) {
                this.$set(data, 'children', []);
            }

            data.children.push(newNode);

            // 更新索引
            this.updateIndexes();

            this.$emit('add-node', {
                parentNode: node,
                parentData: data,
                newNode,
                nodeType: 'module',
                level: node.level,
                treeData: this.treeData
            });
        },

        /**
         * 删除节点
         * @param {Object} node 节点node
         * @param {Object} data 节点数据
         */
        removeNode(node, data) {
            // 检查权限
            if (!this.canEditOrDelete(data)) {
                this.$message.warning('您没有权限删除此节点');
                return;
            }

            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.initInternalData();

                // 收集要删除的节点ID（包括所有子节点）
                this.collectDeletedNodeIds(data);

                const { parent } = node;
                const children = parent.data.children || parent.data;
                const sort = children.findIndex((d) => d.id === data.id);

                if (sort > -1) {
                    children.splice(sort, 1);
                }

                // 更新索引
                this.updateIndexes();

                this.$emit('remove-node', {
                    node,
                    data,
                    parent,
                    treeData: this.treeData
                });
            });
        },

        /**
         * 生成唯一id
         * @returns {string} 唯一ID
         */
        generateId() {
            return uuidv4();
        },

        /**
         * 收集要删除的节点ID（包括所有子节点）
         * @param {Object} nodeData - 节点数据
         */
        collectDeletedNodeIds(nodeData) {
            // 避免重复添加
            if (!this.deletedNodeIdList.includes(nodeData.id)) {
                this.deletedNodeIdList.push(nodeData.id);
            }

            // 递归收集子节点ID
            if (nodeData.children && nodeData.children.length > 0) {
                nodeData.children.forEach((child) => {
                    this.collectDeletedNodeIds(child);
                });
            }
            if (!nodeData.children || nodeData.children.length === 0) {
                this.$emit('delete-node', this.deletedNodeIdList);
            }
        },

        /**
         * 设置节点及其所有子节点的editType
         * @param {Object} nodeData - 节点数据
         * @param {string} editType - 编辑类型 ('add' 或 'edit')
         */
        setNodeEditType(nodeData, editType) {
            this.$set(nodeData, 'editType', editType);

            // 递归设置子节点
            if (nodeData.children && nodeData.children.length > 0) {
                nodeData.children.forEach((child) => {
                    this.setNodeEditType(child, editType);
                });
            }
        },

        /**
         * 设置所有节点的editType
         * @param {string} editType - 编辑类型 ('add' 或 'edit')
         */
        setAllNodesEditType(editType) {
            if (!this.treeData || this.treeData.length === 0) return;

            const setEditTypeRecursive = (nodes) => {
                nodes.forEach((node) => {
                    this.$set(node, 'editType', editType);
                    if (node.children && node.children.length > 0) {
                        setEditTypeRecursive(node.children);
                    }
                });
            };

            setEditTypeRecursive(this.treeData);
        },

        /**
         * 清空所有变更记录
         */
        clearChanges() {
            this.deletedNodeIdList = [];

            // 递归清除所有节点的editType
            const clearEditType = (nodes) => {
                nodes.forEach((node) => {
                    if (node.editType) {
                        this.$delete(node, 'editType');
                    }
                    if (node.originalLabel) {
                        this.$delete(node, 'originalLabel');
                    }

                    if (node.children && node.children.length > 0) {
                        clearEditType(node.children);
                    }
                });
            };

            clearEditType(this.treeData);
        },

        /**
         * 初始化内部数据
         */
        initInternalData() {
            if (!this.internalTreeData && this.data.length > 0) {
                this.internalTreeData = this.$tools.cloneDeep(this.data);
            }
        },

        /**
         * 更新索引
         */
        updateIndexes() {
            const updateNodeIndexes = (nodes) => {
                if (!nodes || !Array.isArray(nodes)) return;

                nodes.forEach((node, sort) => {
                    // 更新当前节点的索引（从1开始）
                    this.$set(node, 'sort', sort + 1);

                    // 递归更新子节点
                    if (node.children && node.children.length > 0) {
                        updateNodeIndexes(node.children);
                    }
                });
            };

            updateNodeIndexes(this.treeData);
        }
    }
};
</script>

<style scoped>
.draggable-tree {
    width: 100%;
}

.tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 8px;
}

.node-label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.node-label:hover {
    background-color: #f5f7fa;
}

.node-label.disabled {
    color: #c0c4cc;
    cursor: not-allowed;
}

.node-label.disabled:hover {
    background-color: transparent;
}

.node-input {
    flex: 1;
    margin-right: 22px;
}

.add-node-popover {
    margin-right: 8px;
}

.node-type-selector {
    text-align: center;
}

.selector-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #303133;
}

.selector-options {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.type-button {
    flex: 1;
    min-width: 60px;
}

.node-sort {
    color: #333;
    font-weight: 600;
    margin-right: 4px;
}
</style>
