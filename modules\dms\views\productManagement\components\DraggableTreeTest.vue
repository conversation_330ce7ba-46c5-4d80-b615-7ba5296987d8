<template>
    <div class="test-container">
        <h2>DraggableTree 状态测试</h2>
        
        <div class="mode-switch">
            <el-radio-group v-model="isAddMode">
                <el-radio :label="true">新增模式 (isAdd=true)</el-radio>
                <el-radio :label="false">编辑模式 (isAdd=false)</el-radio>
            </el-radio-group>
        </div>

        <div class="actions">
            <el-button type="primary" @click="compareStatus">对比状态</el-button>
            <el-button type="success" @click="resetTree">重置</el-button>
        </div>

        <div class="tree-wrapper">
            <DraggableTree
                ref="draggableTree"
                :data="treeData"
                :isAdd="isAddMode"
                :productName="'测试产品'"
                :props="{ children: 'children', label: 'moduleName' }"
            />
        </div>

        <div class="result" v-if="compareResult">
            <h3>对比结果：</h3>
            <div class="result-section">
                <h4>模式：{{ isAddMode ? '新增模式' : '编辑模式' }}</h4>
                <p><strong>新增：</strong>{{ compareResult.compareResult.added.length }} 个</p>
                <p><strong>编辑：</strong>{{ compareResult.compareResult.edited.length }} 个</p>
                <p><strong>删除：</strong>{{ compareResult.compareResult.deleted.length }} 个</p>
                <p><strong>移动：</strong>{{ compareResult.compareResult.moved.length }} 个</p>
            </div>
            
            <div v-if="compareResult.compareResult.edited.length > 0">
                <h4>编辑详情：</h4>
                <ul>
                    <li v-for="item in compareResult.compareResult.edited" :key="item.id">
                        {{ item.originalModuleName }} → {{ item.moduleName }}
                    </li>
                </ul>
            </div>
            
            <div v-if="compareResult.compareResult.moved.length > 0">
                <h4>移动详情：</h4>
                <ul>
                    <li v-for="item in compareResult.compareResult.moved" :key="item.id">
                        {{ item.moduleName }}: {{ item.originalParentPath }} → {{ item.parentPath }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
import DraggableTree from './DraggableTree.vue';

export default {
    name: 'DraggableTreeTest',
    components: {
        DraggableTree
    },
    data() {
        return {
            isAddMode: false,
            compareResult: null,
            treeData: [
                {
                    id: '1',
                    moduleName: '根产品',
                    type: 'product',
                    moduleType: 'P',
                    sort: 1,
                    children: [
                        {
                            id: '2',
                            moduleName: '子产品A',
                            type: 'product',
                            moduleType: 'SP',
                            sort: 1,
                            children: [
                                {
                                    id: '3',
                                    moduleName: '模块1',
                                    type: 'module',
                                    moduleType: 'M',
                                    sort: 1,
                                    children: []
                                },
                                {
                                    id: '4',
                                    moduleName: '模块2',
                                    type: 'module',
                                    moduleType: 'M',
                                    sort: 2,
                                    children: []
                                }
                            ]
                        },
                        {
                            id: '5',
                            moduleName: '子产品B',
                            type: 'product',
                            moduleType: 'SP',
                            sort: 2,
                            children: []
                        }
                    ]
                }
            ]
        };
    },
    methods: {
        compareStatus() {
            const result = this.$refs.draggableTree.compareAndUpdateTreeStatus();
            this.compareResult = result;
            
            if (this.isAddMode) {
                this.$message.info('新增模式：不计算任何编辑状态');
            } else {
                this.$message.success('编辑模式：已完成状态对比');
            }
        },
        
        resetTree() {
            this.$refs.draggableTree.resetToOriginal();
            this.compareResult = null;
            this.$message.info('已重置到原始状态');
        }
    }
};
</script>

<style scoped>
.test-container {
    padding: 20px;
    max-width: 800px;
}

.mode-switch {
    margin-bottom: 15px;
    padding: 10px;
    background: #f5f7fa;
    border-radius: 4px;
}

.actions {
    margin-bottom: 20px;
}

.tree-wrapper {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.result {
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    padding: 15px;
}

.result-section {
    margin-bottom: 15px;
}

.result-section p {
    margin: 5px 0;
}

.result h4 {
    margin: 10px 0 5px 0;
    color: #303133;
}

.result ul {
    margin: 5px 0;
    padding-left: 20px;
}

.result li {
    margin-bottom: 3px;
}
</style>
