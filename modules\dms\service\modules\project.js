/**
 * 项目管理相关服务接口
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 项目管理接口
        project: {
            // 获取项目列表（支持分页和筛选）
            getProjectList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/project/getProjectsPermission',
                    method: 'post',
                    data
                });
            },
            // 获取所有项目（不分页）
            getAllProjects(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/getAllProject',
                    method: 'get',
                    params: data
                });
            },
            // 获取项目详情
            getProjectDetail(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/getProjectInfo',
                    method: 'get',
                    params: data
                });
            },
            // 新增项目
            addProject(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/addProject',
                    method: 'post',
                    data
                });
            },
            // 修改项目信息
            updateProject(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/updateProject',
                    method: 'post',
                    data
                });
            },
            // 删除项目
            deleteProject(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/deleteProject',
                    method: 'get',
                    params: data
                });
            },
            // 获取项目成员
            getProjectMembers(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/getProjectMembers',
                    method: 'post',
                    data
                });
            },
            // 添加项目成员
            addProjectMember(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/addProjectMember',
                    method: 'post',
                    data
                });
            },
            // 移除项目成员
            removeProjectMember(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/removeProjectMember',
                    method: 'get',
                    params: data
                });
            }
        }
    };

    return service;
};
