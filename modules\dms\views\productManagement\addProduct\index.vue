<template>
    <div>
        <!-- 页签菜单 -->
        <el-menu default-active="0" class="" mode="horizontal" @select="handleTabSelect">
            <el-menu-item v-for="(tab, index) in tabsPageConfig.tabItems" :index="index.toString()" :key="tab">{{
                tab
            }}</el-menu-item>
        </el-menu>
        <!-- 动态组件区域 -->
        <component :is="currentComponent"></component>
    </div>
</template>
<script>
import baseInfo from '../baseInfo/index.vue';
import productStructure from '../productStructure/index.vue';
import connectionInfo from '../connectionInfo/index.vue';

export default {
    name: 'AddProduct',
    components: {
        baseInfo,
        productStructure,
        connectionInfo
    },
    data() {
        return {
            currentComponent: 'baseInfo',
            tabsPageConfig: {
                activeName: '基本信息',
                tabItems: ['基本信息', '产品结构', '关联信息']
            }
        };
    },
    methods: {
        handleTabSelect(index) {
            const components = ['baseInfo', 'productStructure', 'connectionInfo'];
            this.currentComponent = components[parseInt(index)];
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-menu.el-menu--horizontal {
    display: flex;
    justify-content: flex-end;
    margin: 1vh 0;
    border-bottom: 3px solid #3370ff;
    .el-menu-item {
        background-color: #fff;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
        color: #4377ee;
        height: 45px;
        line-height: 45px;
    }
    .el-menu-item.is-active {
        background-color: #4377ee;
        color: #fff;
    }
}
</style>
