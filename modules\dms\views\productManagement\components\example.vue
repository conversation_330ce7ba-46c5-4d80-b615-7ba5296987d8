<template>
    <div>
        <DraggableTree
            :data="treeData"
            :isAdd="isAddMode"
            :isTeamLeader="false"
            :productName="productName"
            :props="props"
            @add-node="handleTreeDataChange"
            @remove-node="handleTreeDataChange"
            @node-drop="handleTreeDataChange"
            @label-change="handleTreeDataChange"
            @delete-node="deleteNode"
        />
        <DetailTree :props="props" :data="treeData" />
    </div>
</template>

<script>
import DraggableTree from 'dms/views/productManagement/components/DraggableTree.vue';
import DetailTree from 'dms/views/productManagement/components/DetailTree.vue';

export default {
    components: {
        DraggableTree,
        DetailTree
    },
    data() {
        return {
            // 控制是否为新增模式
            isAddMode: false,
            treeData: [
                {
                    id: 1,
                    moduleName: '打印扫描产品',
                    type: 'product',
                    moduleType: 'P',
                    sort: 1,
                    editType: 'delete',
                    children: [
                        {
                            id: 2,
                            moduleName: '子产品1',
                            type: 'product',
                            moduleType: 'SP',
                            sort: 1,
                            editType: 'delete',

                            children: [
                                {
                                    id: 4,
                                    moduleName: '模块1',
                                    type: 'module',
                                    moduleType: 'M',
                                    sort: 1,
                                    editType: 'edit',
                                    originalLabel: '原名称1'
                                },
                                {
                                    id: 5,
                                    moduleName: '模块2',
                                    type: 'module',
                                    moduleType: 'M',
                                    sort: 2,
                                    editType: 'edit',
                                    originalLabel: '原名称2'
                                }
                            ]
                        },
                        {
                            id: 3,
                            moduleName: '模块1',
                            type: 'module',
                            moduleType: 'M',
                            sort: 2,
                            editType: 'add',
                            children: [
                                {
                                    id: 6,
                                    moduleName: '子模块1',
                                    type: 'module',
                                    moduleType: 'SM',
                                    sort: 1,
                                    editType: 'add'
                                }
                            ]
                        }
                    ]
                }
            ],
            productName: '打印扫描产品',
            props: {
                children: 'children',
                label: 'moduleName'
            }
        };
    },
    methods: {
        handleTreeDataChange({ treeData }) {
            console.log('最终结果:', treeData);
        },
        deleteNode(nodeData) {
            console.log('删除节点:', nodeData);
        }
    }
};
</script>
