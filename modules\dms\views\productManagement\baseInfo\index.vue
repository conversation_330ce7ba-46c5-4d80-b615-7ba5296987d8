<template>
    <div class="view-box">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button type="primary" @click="handleExport()">导入产品</el-button>
            <el-button type="primary" @click="confirm()">发布</el-button>
            <el-button type="info" @click="handleBack()">返回</el-button>
        </div>
        <div>
            <!-- 标题 -->
            <formula-title :title="title"></formula-title>
            <el-form :model="addForm" ref="dataForm" label-width="160px" :rules="userRules">
                <div class="add-flex">
                    <el-form-item label="产品名称" prop="productName">
                        <el-input
                            v-model="addForm.productName"
                            placeholder="请输入产品名称"
                            clearable
                            maxlength="64"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="产品编号" prop="productNumber">
                        <el-input
                            v-model="addForm.productNumber"
                            placeholder="请输入产品编号"
                            clearable
                            maxlength="64"
                        ></el-input>
                    </el-form-item>
                </div>
                <div class="add-flex">
                    <el-form-item label="产品线" prop="owner">
                        <el-select v-model="addForm.owner" placeholder="请选择产品线" clearable>
                            <el-option
                                v-for="item in ownerData"
                                :key="item.loginName"
                                :label="item.employeeName"
                                :value="item.loginName"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="Product Owner" prop="productName">
                        <el-input
                            v-model="addForm.productName"
                            placeholder="请输入Product Owner"
                            clearable
                            maxlength="64"
                        ></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="目标用户" prop="scene">
                    <el-input
                        type="textarea"
                        v-model="addForm.scene"
                        maxlength="500"
                        :rows="4"
                        placeholder="建议参考的模板：<行业/身份>中面临<具体痛点>的<角色>"
                    ></el-input>
                </el-form-item>
                <el-form-item label="产品定位" prop="description">
                    <el-input
                        type="textarea"
                        v-model="addForm.description"
                        placeholder="建议参考的模板：针对<细分市场>的<产品类型>，以<差异化特性>区别于<竞品类别>"
                        maxlength="500"
                        :rows="4"
                    ></el-input>
                </el-form-item>
                <el-form-item label="核心价值" prop="businessValue">
                    <el-input
                        type="textarea"
                        v-model="addForm.businessValue"
                        placeholder="建议参考的模板：功能价值：通过<核心技术/功能>实现<用户获益>，如<使用场景>。情感价值：帮助用户<心理诉求>。量化价值：将<原有指标>从X提升至Y。"
                        maxlength="500"
                        :rows="4"
                    ></el-input>
                </el-form-item>
                <el-form-item label="竞争策略" prop="earnings">
                    <el-input
                        type="textarea"
                        v-model="addForm.earnings"
                        placeholder="建议参考的模板：差异化路径：在<维度>上超越竞品或保持同等水平（如：<具体指标>）。防御策略：通过<壁垒类型>构建护城河，如<专利/生态等>。替代方案转化：针对<竞品用户>的<迁移诱因>。"
                        maxlength="500"
                        :rows="4"
                    ></el-input>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';

export default {
    components: { formulaTitle },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            title: '新建产品',
            addForm: {},
            ownerData: [],
            userRules: {
                productName: [
                    {
                        required: true,
                        message: '请输入需求名称',
                        trigger: 'change'
                    }
                ],
                storyType: [
                    {
                        required: true,
                        message: '请选择类型',
                        trigger: 'change'
                    }
                ],
                owner: [
                    {
                        required: true,
                        message: '请选择需求负责人',
                        trigger: 'change'
                    }
                ],
                deliveryDate: [
                    {
                        required: true,
                        message: '请选择期望交付日期',
                        trigger: 'change'
                    }
                ],
                priority: [
                    {
                        required: true,
                        message: '请选择优先级',
                        trigger: 'change'
                    }
                ],
                scene: [
                    {
                        required: true,
                        message: '请输入用户场景',
                        trigger: 'blur'
                    }
                ],
                description: [
                    {
                        required: true,
                        message: '请输入需求描述',
                        trigger: 'blur'
                    }
                ]
            }
        };
    },
    mounted() {
        this.addShift();
    },
    methods: {
        addShift() {
            this.addForm = {};
            this.$nextTick(() => {
                if (this.$refs.dataForm) {
                    this.$refs.dataForm.resetFields();
                }
            });
        },
        // 保存提交
        confirm() {
            this.$refs.dataForm.validate((valid) => {
                if (valid) {
                    const params = {
                        ...this.addForm
                    };
                    this.$service.dms.original.addOriginal(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success(res.message);
                            this.$router.back();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                }
            });
        },
        // 返回上一页
        handleBack() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped>
.view-box {
    width: 100%;
    padding: 20px;
    height: calc(100vh - 105px);
    overflow: auto;
    display: flex;
    flex-direction: column;
}
.sprint-btn {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
}
.add-flex {
    width: 100%;
    display: flex;
    justify-content: flex-start;
}
</style>
