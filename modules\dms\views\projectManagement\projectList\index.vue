<template>
    <div class="project-management-page">
        <project-list
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :data="projectData"
            :load-data="loadProjectData"
            :total="total"
            :page.sync="page"
            :limit.sync="limit"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @selection-change="handleSelectionChange"
            @pagination="handlePagination"
        >
            <!-- 操作列插槽 -->
            <template #actions="{ row }">
                <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" size="small" @click="handleView(row)">查看</el-button>
                <el-button type="text" size="small" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
            </template>
        </project-list>
    </div>
</template>

<script>
import ProjectList from '../components/projectList/index.vue';

export default {
    name: 'ProjectManagementPage',
    components: {
        ProjectList
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: 'all',
            // 导航栏配置
            navItems: [
                { name: 'all', label: '全部项目' },
                { name: 'my', label: '我的项目' },
                { name: 'active', label: '进行中' },
                { name: 'completed', label: '已完成' }
            ],
            // 查询参数
            queryParams: {
                projectName: '',
                projectLeader: '',
                departmentName: '',
                projectStatus: '',
                projectType: ''
            },
            // 查询表单配置
            queryConfig: {
                elFormAttrs: {
                    'label-width': '80px'
                },
                items: [
                    {
                        prop: 'projectName',
                        label: '项目名称',
                        type: 'input',
                        attrs: {
                            placeholder: '请输入项目名称'
                        }
                    },
                    {
                        prop: 'projectLeader',
                        label: '项目负责人',
                        type: 'input',
                        attrs: {
                            placeholder: '请输入负责人姓名'
                        }
                    },
                    {
                        prop: 'departmentName',
                        label: '所属部门',
                        type: 'input',
                        attrs: {
                            placeholder: '请输入部门名称'
                        }
                    },
                    {
                        prop: 'projectStatus',
                        label: '项目状态',
                        type: 'select',
                        options: [
                            { label: '全部', value: '' },
                            { label: '规划中', value: 'planning' },
                            { label: '进行中', value: 'active' },
                            { label: '已暂停', value: 'paused' },
                            { label: '已完成', value: 'completed' },
                            { label: '已取消', value: 'cancelled' }
                        ]
                    },
                    {
                        prop: 'projectType',
                        label: '项目类型',
                        type: 'select',
                        options: [
                            { label: '全部', value: '' },
                            { label: '产品开发', value: 'product' },
                            { label: '技术研发', value: 'tech' },
                            { label: '运维支持', value: 'ops' },
                            { label: '其他', value: 'other' }
                        ]
                    }
                ]
            },
            // 项目数据
            projectData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10
        };
    },
    mounted() {
        this.loadProjectData();
    },
    methods: {
        // 加载项目数据
        async loadProjectData() {
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    projectType: 'P', // P-项目
                    ...this.queryParams
                };
                
                const response = await this.$service.dms.project.getProjectList(params);
                
                if (response.code === '0000') {
                    this.projectData = response.data?.list || [];
                    this.total = response.data?.total || 0;
                } else {
                    this.$message.error(response.message || '获取项目列表失败');
                }
            } catch (error) {
                console.error('加载项目数据失败:', error);
                this.$message.error('加载项目数据失败');
            }
        },

        // 处理搜索
        handleSearch() {
            this.page = 1; // 重置到第一页
            this.loadProjectData();
        },

        // 处理重置
        handleReset() {
            this.page = 1; // 重置到第一页
            this.loadProjectData();
        },

        // 处理导航切换
        handleNavChange() {
            this.page = 1; // 重置到第一页
            this.loadProjectData();
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            console.log('选中的项目:', selection);
        },

        // 处理分页变化
        handlePagination() {
            this.loadProjectData();
        },

        // 编辑项目
        handleEdit(row) {
            this.$message.info(`编辑项目: ${row.projectName}`);
            // TODO: 实现编辑功能
        },

        // 查看项目详情
        handleView(row) {
            this.$message.info(`查看项目: ${row.projectName}`);
            // TODO: 实现查看功能
        },

        // 删除项目
        handleDelete(row) {
            this.$confirm(`确定要删除项目"${row.projectName}"吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // TODO: 实现删除功能
                this.$message.success('删除成功');
                this.loadProjectData();
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.project-management-page {
    height: 100%;
    padding: 20px;
}
</style>
