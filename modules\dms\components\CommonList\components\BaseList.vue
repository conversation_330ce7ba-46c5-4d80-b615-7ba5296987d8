<template>
    <div class="table-container">
        <el-table class="dms-table" ref="table" :data="tableData" :row-key="rowKey" v-bind="$attrs" v-on="$listeners">
            <el-table-column
                v-if="columns.some((col) => col.type === 'selection')"
                type="selection"
                :selectable="checkSelectable"
            ></el-table-column>
            <!-- 通过 schema 配置的列 -->
            <el-table-column
                v-for="(column, index) in processedColumns"
                :key="column.prop || `${column.type}_${index}`"
                :type="column.type"
                :prop="column.prop"
                :label="column.label"
                :width="column.width"
                :min-width="column.minWidth"
                :fixed="column.fixed"
                :sortable="column.sortable"
                :show-overflow-tooltip="column.showOverflowTooltip !== false"
                v-bind="column.attrs || {}"
            >
                <template v-if="!column.type" slot-scope="scope">
                    <!-- 优先使用具名插槽 -->
                    <slot
                        v-if="$scopedSlots[column.prop]"
                        :name="column.prop"
                        :row="scope.row"
                        :column="scope.column"
                        :index="scope.$index"
                    ></slot>
                    <!-- 显示原始值 -->
                    <span v-else>{{
                        column.render ? column.render(scope.row[column.prop], scope.row) : scope.row[column.prop]
                    }}</span>
                </template>
            </el-table-column>

            <!-- 操作列插槽 -->
            <el-table-column
                v-if="$scopedSlots.actions"
                label="操作"
                :width="actionsWidth"
                :fixed="actionsFixed"
                header-align="center"
            >
                <template slot-scope="scope">
                    <slot name="actions" :row="scope.row" :column="scope.column" :$index="scope.$index"></slot>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'BaseList',
    inheritAttrs: false,
    props: {
        // 列配置 schema
        columns: {
            type: Array,
            default: () => [],
            validator: (columns) => {
                return columns.every((col) => {
                    // 特殊列类型（如 selection, index）不需要 prop 和 label
                    if (col.type && ['selection', 'index', 'expand'].includes(col.type)) {
                        return true;
                    }
                    // 普通列需要 prop 和 label
                    return col.prop && col.label;
                });
            }
        },
        // 数据加载函数（可选，用于刷新数据）
        loadData: {
            type: Function,
            required: false
        },
        // 初始数据
        data: {
            type: Array,
            default: () => []
        },
        // 操作列宽度
        actionsWidth: {
            type: [String, Number],
            default: 150
        },
        // 操作列是否固定
        actionsFixed: {
            type: [String, Boolean],
            default: 'right'
        },
        // 行数据的key
        rowKey: {
            type: [String, Function],
            default: 'id'
        },
        // 自定义行是否可选择的函数
        selectable: {
            type: Function,
            default: null
        }
    },
    data() {
        return {
            tableData: []
        };
    },
    computed: {
        // 处理列配置，表头默认居中
        processedColumns() {
            return this.columns
                .map((column) => ({
                    ...column,
                    attrs: {
                        'header-align': 'center',
                        ...(column.attrs || {})
                    }
                }))
                .filter((column) => column.type !== 'selection');
        }
    },
    watch: {
        data: {
            handler(newData) {
                this.tableData = [...newData];
            },
            immediate: true,
            deep: true
        }
    },

    mounted() {
        // 如果没有初始数据且有加载函数，则加载数据
        if (!this.data.length) {
            this.loadRootData();
        }
    },
    methods: {
        /**
         * 加载数据
         */
        async loadRootData() {
            if (!this.loadData) return;
            try {
                this.loading = true;
                const data = await this.loadData();
                this.tableData = data || [];
            } catch (error) {
                console.error('加载数据失败:', error);
                this.$message.error('加载数据失败');
            }
        },

        /**
         * 刷新表格数据
         */
        refresh() {
            if (this.loadData) {
                this.loadRootData();
            }
        },

        /**
         * 获取表格实例
         * @returns {VueComponent} 表格实例
         */
        getTableRef() {
            return this.$refs.table;
        },

        /**
         * 清除选择
         */
        clearSelection() {
            this.$refs.table.clearSelection();
        },

        /**
         * 获取所有选中的行
         * @returns {Array} 选中的行数据
         */
        getSelection() {
            return this.$refs.table.selection || [];
        },

        /**
         * 设置指定行的选中状态
         * @param {Object} row - 行数据
         * @param {Boolean} selected - 是否选中
         */
        toggleRowSelection(row, selected) {
            if (this.checkSelectable(row)) {
                this.$refs.table.toggleRowSelection(row, selected);
            }
        },

        /**
         * 检查行是否可选择
         * @param {Object} row - 行数据
         * @param {Number} index - 行索引
         * @returns {Boolean} 是否可选择
         */
        checkSelectable(row, index) {
            // 如果有自定义的 selectable 函数，优先使用
            if (this.selectable && typeof this.selectable === 'function') {
                return this.selectable(row, index);
            }
            // 默认所有行都可选择
            return true;
        }
    }
};
</script>

<style lang="scss" scoped>
.table-container {
    position: relative;

    .loading-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        z-index: 10;
    }
}
// 统一表头高度，修正固定列错位
::v-deep .el-table__header {
    padding: 0;
    height: 50px !important;
}
</style>
