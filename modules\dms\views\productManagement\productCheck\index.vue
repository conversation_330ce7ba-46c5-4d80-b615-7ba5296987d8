<!-- 产品审核 -->
<template>
    <div class="view">
        <div class="content query-label-line2">
            <project-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="loadProductData"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @selection-change="handleSelectionChange"
                @pagination="handlePagination"
            >
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button type="text" size="small" @click="handleDetails(row)">详情</el-button>
                    <el-button type="text" size="small" @click="handleAudit(row)">审核</el-button>
                </template>
            </project-list>
        </div>
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, navItems, productCheckColumns } from './config.js';

export default {
    name: 'ProductCheck',
    components: {
        ProjectList
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productCheckColumns,
            // 产品数据
            productData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10
        };
    },
    mounted() {
        this.loadProductData();
    },
    methods: {
        // 加载产品数据
        async loadProductData() {
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    ...this.queryParams
                };

                // 日期处理
                if (params.foundDate && params.foundDate.length > 0) {
                    params.startDateString = params.foundDate[0];
                    params.endDateString = params.foundDate[1];
                }
                if (params.closeDate && params.closeDate.length > 0) {
                    params.closeStartDateString = params.closeDate[0];
                    params.closeEndDateString = params.closeDate[1];
                }

                // 根据导航标签设置任务状态
                if (this.activeNavTab === '') {
                    params.taskState = '所有';
                } else {
                    params.taskState = this.activeNavTab;
                }

                const response = await this.$service.dms.common.getProjectOrGroupList(params);

                if (response.code === '000000') {
                    this.productData = response.result?.list || [];
                    this.total = response.result?.total || 0;
                } else {
                    this.$message.error(response.message || '获取产品审核列表失败');
                }
            } catch (error) {
                console.error('加载产品数据失败:', error);
                this.$message.error('加载产品数据失败');
            }
        },

        // 处理搜索
        handleSearch() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理重置
        handleReset() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理导航切换
        handleNavChange() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            console.log('选中的产品:', selection);
        },

        // 处理分页变化
        handlePagination() {
            this.loadProductData();
        },

        // 查看详情
        handleDetails(row) {
            this.$message.info(`查看产品详情: ${row.productName}`);
            // TODO: 实现查看详情功能
        },

        // 审核产品
        handleAudit(row) {
            this.$message.info(`审核产品: ${row.productName}`);
            // TODO: 实现审核功能
        }
    }
};
</script>

<style lang="scss" scoped></style>
