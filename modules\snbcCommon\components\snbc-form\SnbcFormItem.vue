<template>
    <component :is="config.component" :config="config" @validate="handleValidate">
        <template v-for="(slot, slotName) in $slots" #[slotName]>
            <slot :name="slotName" />
        </template>
    </component>
</template>
<script>
import SnbcFormInput from 'snbcCommon/components/snbc-form/SnbcFormInput.vue';
import SnbcFormInputNumber from 'snbcCommon/components/snbc-form/SnbcFormInputNumber.vue';
import SnbcFormInputNumberRange from 'snbcCommon/components/snbc-form/SnbcFormInputNumberRange.vue';
import SnbcFormTextarea from 'snbcCommon/components/snbc-form/SnbcFormTextarea.vue';
import SnbcFormSelect from 'snbcCommon/components/snbc-form/SnbcFormSelect.vue';
import SnbcFormDatePicker from 'snbcCommon/components/snbc-form/SnbcFormDatePicker.vue';
import SnbcFormDateRangePicker from 'snbcCommon/components/snbc-form/SnbcFormDateRangePicker.vue';
import SnbcFormDictSelect from 'snbcCommon/components/snbc-form/SnbcFormDictSelect.vue';
import SnbcFormRegion from 'snbcCommon/components/snbc-form/SnbcFormRegion.vue';
import SnbcFormFileUpload from 'snbcCommon/components/snbc-form/SnbcFormFileUpload.vue';
import SnbcFormRadio from 'snbcCommon/components/snbc-form/SnbcFormRadio.vue';
import SnbcFormCheckbox from 'snbcCommon/components/snbc-form/SnbcFormCheckbox.vue';
import SnbcFormMultiCitySelect from 'snbcCommon/components/snbc-form/SnbcFormMultiCitySelect.vue';
import SnbcFormMultiAreaSelect from 'snbcCommon/components/snbc-form/SnbcFormMultiAreaSelect.vue';
import SnbcFormCityManagerSelect from 'snbcCommon/components/snbc-form/SnbcFormCityManagerSelect.vue';
import SnbcFormPeopleSelector from 'snbcCommon/components/snbc-form/SnbcFormPeopleSelector.vue';
// 服务众维商下拉组件
import SnbcFormProviderSelect from 'snbcCommon/components/snbc-form/SnbcFormProviderSelect.vue';
import SnbcFormAreaSelect from 'snbcCommon/components/snbc-form/SnbcFormAreaSelect.vue';
import SnbcFormAreaProvinceSelect from 'snbcCommon/components/snbc-form/SnbcFormAreaProvinceSelect.vue';
import SnbcFormProductLineSelector from 'snbcCommon/components/snbc-form/SnbcFormProductLineSelector.vue';
import SnbcFormProductType from 'snbcCommon/components/snbc-form/SnbcFormProductType.vue';
import SnbcFormSiteSelect from 'snbcCommon/components/snbc-form/SnbcFormSiteSelect.vue';
import SnbcFormTransfer from 'snbcCommon/components/snbc-form/SnbcFormTransfer.vue';
import SnbcFormInputRange from 'snbcCommon/components/snbc-form/SnbcFormInputRange.vue';
import SnbcFormDateMonthPicker from 'snbcCommon/components/snbc-form/SnbcFormDateMonthPicker.vue';

export default {
    // 动态组件，根据config.component值不同应用不同的表单组件
    name: 'SnbcFormItem',
    components: {
        SnbcFormInput,
        SnbcFormInputNumber,
        SnbcFormInputNumberRange,
        SnbcFormTextarea,
        SnbcFormSelect,
        SnbcFormDatePicker,
        SnbcFormDateRangePicker,
        SnbcFormDictSelect,
        SnbcFormRegion,
        SnbcFormFileUpload,
        SnbcFormRadio,
        SnbcFormCheckbox,
        SnbcFormMultiCitySelect,
        SnbcFormPeopleSelector,
        SnbcFormCityManagerSelect,
        SnbcFormMultiAreaSelect,
        SnbcFormProviderSelect,
        SnbcFormAreaSelect,
        SnbcFormAreaProvinceSelect,
        SnbcFormProductLineSelector,
        SnbcFormProductType,
        SnbcFormSiteSelect,
        SnbcFormTransfer,
        SnbcFormInputRange,
        SnbcFormDateMonthPicker
    },
    props: {
        /**
         * SnbcFormItem组件配置
         */
        config: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {};
    },
    computed: {},
    methods: {
        handleValidate(prop) {
            this.$emit('validate', prop);
        }
    }
};
</script>
