<template>
    <div class="group-management-page">
        <group-list
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :data="groupData"
            :load-data="loadGroupData"
            :total="total"
            :page.sync="page"
            :limit.sync="limit"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @selection-change="handleSelectionChange"
            @pagination="handlePagination"
        >
            <!-- 操作列插槽 -->
            <template #actions="{ row }">
                <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" size="small" @click="handleView(row)">查看</el-button>
                <el-button type="text" size="small" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
            </template>
        </group-list>
    </div>
</template>

<script>
import GroupList from '../components/groupList/index.vue';

export default {
    name: 'GroupManagementPage',
    components: {
        GroupList
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: 'all',
            // 导航栏配置
            navItems: [
                { name: 'all', label: '全部团队' },
                { name: 'my', label: '我的团队' },
                { name: 'active', label: '活跃团队' }
            ],
            // 查询参数
            queryParams: {
                teamName: '',
                teamLeader: '',
                departmentName: '',
                status: ''
            },
            // 查询表单配置
            queryConfig: {
                elFormAttrs: {
                    'label-width': '80px'
                },
                items: [
                    {
                        prop: 'teamName',
                        label: '团队名称',
                        type: 'input',
                        attrs: {
                            placeholder: '请输入团队名称'
                        }
                    },
                    {
                        prop: 'teamLeader',
                        label: '团队负责人',
                        type: 'input',
                        attrs: {
                            placeholder: '请输入负责人姓名'
                        }
                    },
                    {
                        prop: 'departmentName',
                        label: '所属部门',
                        type: 'input',
                        attrs: {
                            placeholder: '请输入部门名称'
                        }
                    },
                    {
                        prop: 'status',
                        label: '状态',
                        type: 'select',
                        options: [
                            { label: '全部', value: '' },
                            { label: '活跃', value: 'active' },
                            { label: '暂停', value: 'paused' },
                            { label: '已解散', value: 'disbanded' }
                        ]
                    }
                ]
            },
            // 团队数据
            groupData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10
        };
    },
    mounted() {
        this.loadGroupData();
    },
    methods: {
        // 加载团队数据
        async loadGroupData() {
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    projectType: 'T', // T-团队
                    ...this.queryParams
                };

                const response = await this.$service.dms.group.getGroupList(params);

                if (response.code === '0000') {
                    this.groupData = response.data?.list || [];
                    this.total = response.data?.total || 0;
                } else {
                    this.$message.error(response.message || '获取团队列表失败');
                }
            } catch (error) {
                console.error('加载团队数据失败:', error);
                this.$message.error('加载团队数据失败');
            }
        },

        // 处理搜索
        handleSearch() {
            this.page = 1; // 重置到第一页
            this.loadGroupData();
        },

        // 处理重置
        handleReset() {
            this.page = 1; // 重置到第一页
            this.loadGroupData();
        },

        // 处理导航切换
        handleNavChange() {
            this.page = 1; // 重置到第一页
            this.loadGroupData();
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            console.log('选中的团队:', selection);
        },

        // 处理分页变化
        handlePagination() {
            this.loadGroupData();
        },

        // 编辑团队
        handleEdit(row) {
            this.$message.info(`编辑团队: ${row.teamName}`);
            // TODO: 实现编辑功能
        },

        // 查看团队详情
        handleView(row) {
            this.$message.info(`查看团队: ${row.teamName}`);
            // TODO: 实现查看功能
        },

        // 删除团队
        handleDelete(row) {
            this.$confirm(`确定要删除团队"${row.teamName}"吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    // TODO: 实现删除功能
                    this.$message.success('删除成功');
                    this.loadGroupData();
                })
                .catch(() => {
                    this.$message.info('已取消删除');
                });
        }
    }
};
</script>

<style lang="scss" scoped>
.group-management-page {
    height: 100%;
    padding: 20px;
}
</style>
