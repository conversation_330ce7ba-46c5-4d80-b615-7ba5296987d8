<!-- 变更列表 -->
<template>
    <div class="view">
        <div class="content query-label-line2">
            <snbc-base-table ref="tableRef" :table-config="tableConfig">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
        </div>
    </div>
</template>

<script>
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'snbcCommon/components/snbc-table/SnbcTableTabs.vue';
import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, monthRange } = CommonItems;

// 产品线
const productLine = {
    ...select,
    name: '产品线',
    modelKey: 'productLine'
};
const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName'
};
const productNumber = {
    ...input,
    name: '产品编号',
    modelKey: 'productName'
};
const productId = {
    ...input,
    name: '产品ID',
    modelKey: 'productId'
};
const productOwner = {
    ...input,
    name: 'Product Owner',
    modelKey: 'productOwner'
};
const approver = {
    ...input,
    name: '审批人',
    modelKey: 'approver'
};
const foundDate = {
    ...monthRange,
    name: '申请日期',
    modelKey: 'foundDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};
const closeDate = {
    ...monthRange,
    name: '审批日期',
    modelKey: 'closeDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};
// 查询区域配置项
const queryConfigItems = [
    productLine,
    productName,
    productNumber,
    productId,
    productOwner,
    approver,
    foundDate,
    closeDate
];
// 查询条件参数
const queryParams = {
    productLine: '',
    productName: '',
    productNumber: '',
    productId: '',
    productOwner: '',
    approver: '',
    foundDate: [],
    closeDate: []
};
const tabsConfig = {
    activeName: '所有',
    tabItems: ['所有', '待审核', '已通过', '已拒绝', '已撤回']
};
export default {
    name: 'ChangeList',
    components: {
        SnbcBaseTable,
        SnbcTableTabs
    },
    data() {
        return {
            // 标签页配置对象
            tabsConfig: Object.assign(tabsConfig, {
                handleTabClick: this.handleTabClick
            }),
            tableConfig: {
                // 查询参数
                queryParams: this.$tools.cloneDeep(queryParams),
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: this.$service.dms.common.getProjectOrGroupList,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    { label: 'ID', prop: 'productId', show: true, minWidth: 160 },
                    { label: '产品线', prop: 'productLine', show: true, minWidth: 160 },
                    { label: '产品编号', prop: 'productNumber', show: true, minWidth: 160 },
                    { label: '产品名称', prop: 'productName', show: true, minWidth: 160 },
                    { label: 'Product Owner', prop: 'productOwner', show: true, minWidth: 160 },
                    { label: '审批人', prop: 'foundDate', show: true, minWidth: 160 },
                    { label: '申请日期', prop: 'approver', show: true, minWidth: 160 },
                    { label: '审批结果', prop: 'showMoney', show: true, minWidth: 160 },
                    { label: '审批日期', prop: 'closeDate', show: true, minWidth: 160 }
                ],
                // 操作列配置
                operationColumnWidth: 220,
                operations: [
                    {
                        name: '详情',
                        type: 'success',
                        handleClick: this.handleDetails
                    },
                    {
                        name: '编辑',
                        type: 'success',
                        handleClick: this.handleDetails
                    },
                    {
                        name: '撤回',
                        type: 'success',
                        handleClick: this.handleDetails
                    },
                    {
                        name: '删除',
                        type: 'danger',
                        handleClick: this.handleDetails
                    }
                ],
                hooks: {
                    queryParamsHook: this.queryParamsHook
                }
            }
        };
    },
    mounted() {
        this.handleQuery();
    },
    methods: {
        /**
         * 标签切换处理函数
         */
        handleTabClick() {
            this.handleQuery();
        },
        // 查询前参数处理
        queryParamsHook(params) {
            // 日期处理
            if (params.foundDate && params.foundDate.length > 0) {
                params.startDateString = params.foundDate[0];
                params.endDateString = params.foundDate[1];
            }
            if (params.closeDate && params.closeDate.length > 0) {
                params.closeStartDateString = params.closeDate[0];
                params.closeEndDateString = params.closeDate[1];
            }
            const { activeName } = this.tabsConfig;
            if (activeName === '所有') {
                params.taskState = '所有';
            }
            if (activeName === '待审核') {
                params.taskState = '待审核';
            }
            if (activeName === '已通过') {
                params.taskState = '已通过';
            }
            if (activeName === '已拒绝') {
                params.taskState = '已拒绝';
            } else {
                params.taskState = '已撤回';
            }
        },
        handleQuery() {
            this.$refs.tableRef.queryList();
        }
        /**
         * 详情
         * @param {Object} row 每行的数据
         */
    }
};
</script>

<style lang="scss" scoped></style>
