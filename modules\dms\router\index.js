// 目前框架路由，只支持到二级路由
export default [
    {
        path: '/app/dms/groupManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsGroupManagement',
        meta: { title: '团队管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'groupProfile',
                component: () => import('../views/groupManagement/groupProfile/index'),
                noPermission: true,
                name: 'GroupProfile',
                meta: { title: '团队概况', icon: 'fa fa-square' }
            },
            {
                path: 'schedule',
                component: () => import('../views/groupManagement/schedule/index'),
                noPermission: true,
                name: 'Schedule',
                meta: { title: '进度计划', icon: 'fa fa-square' }
            },
            {
                path: 'resourceManagement',
                component: () => import('../views/groupManagement/resourceManagement'),
                noPermission: true,
                name: 'GroupResourceManagement',
                meta: { title: '资源管理', icon: 'fa fa-square' }
            },
            {
                path: 'PlanEvalute',
                component: () => import('../views/groupManagement/schedule/plan-evalute.vue'),
                name: 'PlanEvalute',
                meta: { title: '计划管理冲刺评价', icon: 'fa el-icon-document' }
            },
            {
                path: 'SprintCheck',
                component: () => import('../views/groupManagement/schedule/sprint-check.vue'),
                name: 'SprintCheck',
                meta: { title: '冲刺评价审核', icon: 'fa el-icon-document' }
            },
            {
                path: 'PlanVersion',
                component: () => import('../views/groupManagement/schedule/plan-version.vue'),
                name: 'PlanVersion',
                meta: { title: '计划版本详情', icon: 'fa el-icon-document' }
            },
            {
                path: 'PlanChange',
                component: () => import('../views/groupManagement/schedule/plan-change.vue'),
                name: 'PlanChange',
                meta: { title: '计划变更审核', icon: 'fa el-icon-document' }
            },
            {
                path: 'PlanAdjust',
                component: () => import('../views/groupManagement/schedule/plan-adjust.vue'),
                name: 'PlanAdjust',
                meta: { title: '计划调整', icon: 'fa el-icon-document' }
            },
            {
                path: 'groupList',
                component: () => import('../views/groupManagement/groupList/index.vue'),
                name: 'GroupList',
                meta: { title: '团队列表', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            }
        ]
    },
    {
        path: '/app/dms/departmentManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsDepartmentManagement',
        meta: { title: '部门管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'resourceManagement',
                component: () => import('../views/departmentManagement/resourceManagement'),
                noPermission: true,
                name: 'DepartmentResourceManagement',
                meta: { title: '资源管理', icon: 'fa fa-square' }
            },
            {
                path: 'personnelDashboard',
                component: () => import('../views/departmentManagement/resourceManagement/personnelDashboard'),
                noPermission: true,
                name: 'PersonnelDashboard',
                meta: { title: '个人看板', icon: 'fa fa-square' }
            }
        ]
    },
    {
        path: '/app/dms/projectManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsProjectManagement',
        meta: { title: '项目管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'resourceManagement',
                component: () => import('../views/projectManagement/resourceManagement'),
                noPermission: true,
                name: 'ProjectManagementResourceManagement',
                meta: { title: '资源管理', icon: 'fa fa-square' }
            }
        ]
    },
    {
        path: '/app/dms/demandManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsDemandManagement',
        meta: { title: '需求管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'AddRequire',
                component: () => import('../views/demandManagement/add-require'),
                name: 'AddRequire',
                meta: { title: '新建需求', icon: 'fa el-icon-document' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'demandList',
                component: () => import('../views/demandManagement/demandList'),
                name: 'DemandList',
                meta: { title: '需求列表', icon: 'fa el-icon-document' }
            },
            {
                path: 'OriginalRequire',
                component: () => import('../views/demandManagement/originalRequire/index'),
                name: 'OriginalRequire',
                meta: { title: '我负责的', icon: 'fa fa-square' }
            },
            {
                path: 'CreateRequire',
                component: () => import('../views/demandManagement/originalRequire/create-require.vue'),
                name: 'CreateRequire',
                meta: { title: '创建需求', icon: 'fa fa-square' },
                hidden: true
            },
            {
                path: 'auditDemand',
                component: () => import('../views/demandManagement/auditDemand/index.vue'),
                noPermission: true,
                name: 'AuditDemand',
                meta: { title: '需求审核', icon: 'fa fa-square' }
            },
            {
                path: 'myProposal',
                component: () => import('../views/demandManagement/myProposal/index.vue'),
                noPermission: true,
                name: 'MyProposal',
                meta: { title: '我提出的', icon: 'fa fa-square' }
            },
            {
                path: 'ChangeRequire',
                component: () => import('../views/demandManagement/change-require'),
                hidden: true,
                noPermission: true,
                name: 'ChangeRequire',
                meta: { title: '需求变更', icon: 'fa fa-square' }
            }
        ]
    },
    {
        path: '/app/dms/productManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsProductManagement',
        meta: { title: '产品管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'example',
                component: () => import('../views/productManagement/components/example.vue'),
                name: 'Example',
                meta: { title: 'test', icon: 'fa el-icon-document' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'ProductList',
                component: () => import('../views/productManagement/productList/index.vue'),
                name: 'ProductList',
                meta: { title: '产品列表', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'AddProduct',
                component: () => import('../views/productManagement/addProduct/index.vue'),
                name: 'AddProduct',
                meta: { title: '新建产品', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'ChangeList',
                component: () => import('../views/productManagement/changeList/index.vue'),
                name: 'ChangeList',
                meta: { title: '变更列表', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'ProductCheck',
                component: () => import('../views/productManagement/productCheck/index.vue'),
                name: 'ProductCheck',
                meta: { title: '产品审核', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'MyProduct',
                component: () => import('../views/productManagement/myProduct/index.vue'),
                name: 'MyProduct',
                meta: { title: '我的产品', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            }
        ]
    }
];
