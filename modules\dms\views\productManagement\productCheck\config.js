import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, monthRange } = CommonItems;

// 产品线
const productLine = {
    ...select,
    name: '产品线',
    modelKey: 'productLine'
};

const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName'
};

const productNumber = {
    ...input,
    name: '产品编号',
    modelKey: 'productNumber'
};

const productId = {
    ...input,
    name: '产品ID',
    modelKey: 'productId'
};

const productOwner = {
    ...input,
    name: 'Product Owner',
    modelKey: 'productOwner'
};

const approver = {
    ...input,
    name: '申请人',
    modelKey: 'approver'
};

const checKReason = {
    ...select,
    name: '审批结果',
    modelKey: 'checKReason'
};

const foundDate = {
    ...monthRange,
    name: '申请日期',
    modelKey: 'foundDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};

const closeDate = {
    ...monthRange,
    name: '审批日期',
    modelKey: 'closeDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};

// 查询表单配置
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [productLine, productName, productNumber, productId, productOwner, approver, foundDate, checKReason, closeDate]
};

// 查询条件参数
export const queryParams = {
    productLine: '',
    productName: '',
    productNumber: '',
    productId: '',
    productOwner: '',
    approver: '',
    foundDate: [],
    checKReason: '',
    closeDate: []
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'taskState' },
    { field: '待审核', name: '待审核', queryField: 'taskState' },
    { field: '已审核', name: '已审核', queryField: 'taskState' }
];

// 产品审核列表表格列配置
export const productCheckColumns = [
    {
        type: 'selection',
        width: 55,
        label: '选择框',
        columnManage: {
            sortableDisabled: true,
            pinnedFirst: true,
            widthDisabled: true
        }
    },
    {
        prop: 'index',
        label: '序号',
        width: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productId',
        label: 'ID',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productNumber',
        label: '产品编号',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productName',
        label: '产品名称',
        width: 200,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productOwner',
        label: 'Product Owner',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'approver',
        label: '申请人',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'foundDate',
        label: '申请日期',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'checKReason',
        label: '审批结果',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'closeDate',
        label: '审批日期',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    }
];
